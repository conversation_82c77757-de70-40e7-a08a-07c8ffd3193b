// TODO: Add more details to the manifest
export const APP_MANIFEST = () => ({
  app: {
    name: 'Slack',
    icons: {
      large:
        'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/306_Slack_logo-512.png',
      small:
        'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/306_Slack_logo-512.png',
    },
    category: 'slack',
    description: 'Thena Slack Integration',
    supported_locales: ['en-US'],
  },
  events: {
    publish: [
      {
        event: 'slack:member:joined',
        reason: 'Notify when a member joins workspace',
        schema: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
            },
            user: {
              type: 'string',
            },
            channel: {
              type: 'string',
            },
            channel_type: {
              type: 'string',
            },
            team: {
              type: 'string',
            },
            inviter: {
              type: 'string',
            },
            userType: {
              type: ['string', 'null'],
              enum: ['internal_member', 'customer'],
            },
            userInfo: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                },
                email: {
                  type: 'string',
                },
                name: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
      {
        event: 'slack:member:left',
        reason: 'Notify when a member leaves workspace',
        schema: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
            },
            user: {
              type: 'string',
            },
            channel: {
              type: 'string',
            },
            userType: {
              type: ['string', 'null'],
              enum: ['internal_member', 'customer'],
            },
            userInfo: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                },
                email: {
                  type: 'string',
                },
                name: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
      {
        event: 'slack:channel:created',
        reason: 'Notify when a new channel is created',
        schema: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
            },
            channel: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                },
                name: {
                  type: 'string',
                },
                created: {
                  type: 'number',
                },
                creator: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
      {
        event: 'slack:channel:archived',
        reason: 'Notify when a channel is archived',
        schema: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
            },
            channel: {
              type: 'string',
            },
            user: {
              type: 'string',
            },
          },
        },
      },
      {
        event: 'slack:channel:deleted',
        reason: 'Notify when a channel is deleted',
        schema: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
            },
            channel: {
              type: 'string',
            },
          },
        },
      },
      {
        event: 'slack:message',
        reason: 'Notify when a message is sent in a channel',
        schema: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
            },
            ts: {
              type: 'string',
            },
            thread_ts: {
              type: ['string', 'null'],
            },
            text: {
              type: 'string',
            },
            user: {
              type: 'string',
            },
            channel: {
              type: 'string',
            },
            team: {
              type: 'string',
            },
            blocks: {
              type: ['array', 'null'],
              items: { type: 'object' },
            },
            attachments: {
              type: ['array', 'null'],
              items: { type: 'object' },
            },
            parent_user_id: {
              type: ['string', 'null'],
            },
            subtype: {
              type: ['string', 'null'],
            },
            event_ts: {
              type: ['string', 'null'],
            },
          },
        },
      },
      {
        event: 'slack:reaction:added',
        reason: 'Notify when a reaction is added to a message',
        schema: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
            },
            user: {
              type: 'string',
            },
            reaction: {
              type: 'string',
            },
            item_user: {
              type: 'string',
            },
            item: {
              type: 'object',
              properties: {
                type: {
                  type: 'string',
                },
                channel: {
                  type: 'string',
                },
                ts: {
                  type: 'string',
                },
              },
            },
            event_ts: {
              type: 'string',
            },
          },
        },
      },
      {
        event: 'slack:reaction:removed',
        reason: 'Notify when a reaction is removed from a message',
        schema: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
            },
            user: {
              type: 'string',
            },
            reaction: {
              type: 'string',
            },
            item_user: {
              type: 'string',
            },
            item: {
              type: 'object',
              properties: {
                type: {
                  type: 'string',
                },
                channel: {
                  type: 'string',
                },
                ts: {
                  type: 'string',
                },
              },
            },
            event_ts: {
              type: 'string',
            },
          },
        },
      },
    ],
    subscribe: [
      {
        event: 'account.created',
        reason: 'Sync new accounts to Hubspot',
        description:
          'Create companies in Hubspot when accounts are added to Thena',
      },
      {
        event: 'account.updated',
        reason: 'Sync account updates to Hubspot',
        description: 'Update Hubspot when account details change',
      },
      {
        event: 'contact.created',
        reason: 'Sync new contacts to Hubspot',
        description: 'Create contacts in Hubspot when added to Thena',
      },
      {
        event: 'contact.updated',
        reason: 'Sync contact updates to Hubspot',
        description: 'Update Hubspot when contact details change',
      },
    ],
  },
  scopes: {
    optional: {
      platform: [],
    },
    required: {
      platform: [
        {
          scope: 'ticket',
          reason: 'test',
          description: 'testwetste',
        },
      ],
    },
  },
  metadata: {
    is_privileged_app: true,
  },
  developer: {
    name: 'Thena',
    website: 'https://thena.app',
    terms_url: 'https://thena.app/terms',
    support_email: '<EMAIL>',
    documentation_url: 'https://developers.thena.app/docs',
    privacy_policy_url: 'https://thena.app/privacy',
  },
  activities: [
    {
      name: 'slack.getMembers',
      description: 'Get all members from slack',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'GET',
        endpoint_url: 'https://slack-app.thena.ai/v1/slack/activities/members',
      },
      request_schema: {},
      response_schema: {
        type: 'object',
        properties: {
          data: {
            type: 'object',
          },
          meta: {
            type: 'object',
          },
        },
      },
    },
    {
      name: 'slack.postMessage',
      description: 'Sends a message on Slack',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/post-message',
      },
      request_schema: {
        type: 'object',
        required: ['channel', 'text'],
        properties: {
          text: {
            type: 'string',
            description: 'Message text content',
          },
          blocks: {
            type: 'array',
            description: 'Optional Slack block kit elements',
          },
          channel: {
            type: 'string',
            description: 'Channel ID or user ID to send message to',
          },
          threadTs: {
            type: ['string', 'null'],
            description: 'Optional timestamp of parent message for threading',
          },
          unfurlLinks: {
            type: ['boolean', 'null'],
            description:
              'Optional boolean to determine if links should be unfurled',
          },
          unfurlMedia: {
            type: ['boolean', 'null'],
            description:
              'Optional boolean to determine if media should be unfurled',
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
          ts: {
            type: 'string',
          },
          channel: {
            type: 'string',
          },
          message: {
            type: 'object',
            properties: {
              text: {
                type: 'string',
              },
            },
          },
        },
      },
    },
    {
      name: 'slack.editMessage',
      description: 'Edits an existing message in Slack',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/update-message',
      },
      request_schema: {
        type: 'object',
        required: ['channel', 'ts', 'text'],
        properties: {
          ts: {
            type: 'string',
            description: 'Timestamp of message to edit',
          },
          text: {
            type: 'string',
            description: 'New message text',
          },
          blocks: {
            type: ['string', 'null'],
            description: 'Optional updated block kit elements JSON stringified',
          },
          channel: {
            type: 'string',
            description: 'Channel ID containing the message',
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
          ts: {
            type: ['string', 'null'],
          },
          text: {
            type: ['string', 'null'],
          },
          channel: {
            type: ['string', 'null'],
          },
        },
      },
    },
    {
      name: 'slack.deleteMessage',
      description: 'Deletes a message from a channel',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/delete-message',
      },
      request_schema: {
        type: 'object',
        required: ['channel', 'ts'],
        properties: {
          ts: {
            type: 'string',
            description: 'Timestamp of message to delete',
          },
          channel: {
            type: 'string',
            description: 'Channel ID containing the message',
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
        },
      },
    },
    {
      name: 'slack.addReaction',
      description: 'Adds a reaction emoji to a message',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/add-reaction',
      },
      request_schema: {
        type: 'object',
        required: ['channel', 'ts', 'name'],
        properties: {
          ts: {
            type: 'string',
            description: 'Timestamp of target message',
          },
          name: {
            type: 'string',
            description: 'Emoji name without colons',
          },
          channel: {
            type: 'string',
            description: 'Channel ID containing the message',
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
        },
      },
    },
    {
      name: 'slack.removeReaction',
      description: 'Removes a reaction emoji from a message',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/remove-reaction',
      },
      request_schema: {
        type: 'object',
        required: ['channel', 'ts', 'name'],
        properties: {
          ts: {
            type: 'string',
            description: 'Timestamp of target message',
          },
          name: {
            type: 'string',
            description: 'Emoji name to remove',
          },
          channel: {
            type: 'string',
            description: 'Channel ID containing the message',
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
        },
      },
    },
    {
      name: 'slack.addMember',
      description: 'Adds a user to a channel',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/invite-user-to-conversation',
      },
      request_schema: {
        type: 'object',
        required: ['channel', 'user'],
        properties: {
          user: {
            type: 'string',
            description: 'User ID to add',
          },
          channel: {
            type: 'string',
            description: 'Channel ID',
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
        },
      },
    },
    {
      name: 'slack.removeMember',
      description: 'Removes a user from a channel',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/kick-user-from-conversation',
      },
      request_schema: {
        type: 'object',
        required: ['channel', 'user'],
        properties: {
          user: {
            type: 'string',
            description: 'User ID to remove',
          },
          channel: {
            type: 'string',
            description: 'Channel ID',
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
        },
      },
    },
    {
      name: 'slack.joinChannel',
      description: 'Joins a public channel',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/join-conversation',
      },
      request_schema: {
        type: 'object',
        required: ['channel'],
        properties: {
          channel: {
            type: 'string',
            description: 'Channel ID to join',
          },
          platformTeam: {
            type: 'string',
            description: 'Platform team ID',
          },
          channelType: {
            type: ['string', 'null'],
            description: 'Optional channel type',
            enum: [
              'customer_channel',
              'triage_channel',
              'internal_helpdesk',
              'not_setup',
              'not_configured',
            ],
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
          channel: {
            type: ['object', 'null'],
          },
        },
      },
    },
    {
      name: 'slack.leaveChannel',
      description: 'Leaves a channel',
      http_config: {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': '${installation.bot_token}',
        },
        httpVerb: 'POST',
        endpoint_url:
          'https://slack-app.thena.ai/v1/slack/activities/leave-conversation',
      },
      request_schema: {
        type: 'object',
        required: ['channel'],
        properties: {
          channel: {
            type: 'string',
            description: 'Channel ID to leave',
          },
        },
      },
      response_schema: {
        type: 'object',
        properties: {
          ok: {
            type: 'boolean',
          },
        },
      },
    },
  ],
  integration: {
    webhooks: {
      events: 'https://slack-app.thena.ai/v1/platform/events',
      installations: 'https://slack-app.thena.ai/v1/platform/installations',
    },
    entry_points: {
      main: 'https://app.thena.io/hubspot/integration',
      configuration: 'https://app.thena.io/hubspot/config',
      oauth_redirect: 'https://app.thena.io/hubspot/oauth/callback',
    },
    interactivity: {
      request_url: 'https://api.thena.io/hubspot/interactive',
      message_menu_option_url: 'https://api.thena.io/hubspot/menu-options',
    },
  },
  configuration: {
    optional_settings: [],
    required_settings: [],
  },
});
