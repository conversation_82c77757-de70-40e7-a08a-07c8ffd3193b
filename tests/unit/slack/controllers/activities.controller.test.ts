import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import {
  InstallationStatus,
  Installations,
} from '../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { SlackActivitiesController } from '../../../../src/slack/controllers/activities.controller';
import {
  AddReactionDTO,
  DeleteMessageDTO,
  InviteUserToConversationDTO,
  JoinConversationDTO,
  KickUserFromConversationDTO,
  LeaveConversationDTO,
  PostMessageDTO,
  RemoveReactionDTO,
  UpdateMessageDTO,
} from '../../../../src/slack/dtos/activities.dto';
import { SlackWebAPIService } from '../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ILogger } from '../../../../src/utils';

describe('SlackActivitiesController', () => {
  let controller: SlackActivitiesController;
  let mockLogger: ILogger;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockTeamsRepository: any;
  let mockChannelsRepository: any;
  let mockTeamChannelMapsRepository: any;
  let mockSlackMembersService: any;
  let botCtx: BotCtx;

  const createMockInstallation = (
    id: string,
    teamId: string,
  ): Installations => ({
    id,
    name: `Installation ${id}`,
    installationDump: {
      team: { id: teamId },
      enterprise: undefined,
      user: {
        id: `user-${id}`,
        token: `token-${id}`,
        scopes: ['chat:write'],
      },
    },
    teamId,
    domains: 'example.com',
    teamName: `Team ${id}`,
    enterpriseId: null,
    teamInfo: {},
    status: InstallationStatus.SYNCED,
    installingUserId: `user-${id}`,
    installingUserSlackId: `slack-user-${id}`,
    installingUserName: `User ${id}`,
    botToken: `xoxb-token-${id}`,
    botSlackId: `bot-${id}`,
    botSlackUserId: `bot-user-${id}`,
    slackAppAuthToken: `auth-token-${id}`,
    disconnected: false,
    disconnectedOn: undefined as unknown as Date,
    platformDump: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined as unknown as Date,
    bots: [],
    channels: [],
    users: [],
    teams: [],
    subgroups: [],
    organization: null as any,
  });

  const createMockOrganization = (id: string): Organizations => ({
    id,
    externalPk: `ext-${id}`,
    name: `Organization ${id}`,
    uid: `org-uid-${id}`,
    apiKey: `api-key-${id}`,
    installingUserId: `user-${id}`,
    metadata: {
      applicationId: `app-${id}`,
      installationId: `inst-${id}`,
      createdBy: `user-${id}`,
    },
    bots: [],
    users: [],
    channels: [],
    installations: [],
    teams: [],
    subgroups: [],
  });

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the installations and organization
    const mockInstallation = createMockInstallation('1', 'T123456');
    const mockOrganization = createMockOrganization('1');

    // Set up the bot context
    botCtx = {
      installations: [mockInstallation],
      installation: mockInstallation,
      organization: mockOrganization,
    };

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    // Mock the slack web API service
    mockSlackWebAPIService = {
      sendMessage: vi.fn(),
      updateMessage: vi.fn(),
      deleteMessage: vi.fn(),
      addReactionToMessage: vi.fn(),
      removeReactionFromMessage: vi.fn(),
      inviteUserToConversation: vi.fn(),
      kickUserFromConversation: vi.fn(),
      joinConversation: vi.fn(),
      leaveConversation: vi.fn(),
    } as unknown as SlackWebAPIService;

    // Mock the repositories and services
    mockTeamsRepository = {
      findByCondition: vi.fn(),
    };

    mockChannelsRepository = {
      upsert: vi.fn(),
      findByCondition: vi.fn(),
    };

    mockTeamChannelMapsRepository = {
      exists: vi.fn(),
      upsert: vi.fn(),
    };

    mockSlackMembersService = {
      getAllMembers: vi.fn(),
    };

    // Create the controller
    controller = new SlackActivitiesController(
      mockLogger,
      mockSlackWebAPIService,
      mockTeamsRepository,
      mockChannelsRepository,
      mockTeamChannelMapsRepository,
      mockSlackMembersService,
    );
  });

  describe('postMessage', () => {
    const postMessageDto: PostMessageDTO = {
      channel: 'C12345',
      text: 'Hello world',
      blocks: JSON.stringify([
        { type: 'section', text: { type: 'mrkdwn', text: 'Hello world' } },
      ]),
      threadTs: '1234567890.123456',
    };

    it('should post a message successfully', async () => {
      // Setup
      const mockResponse = { ts: '1234567890.123456', ok: true };
      (mockSlackWebAPIService.sendMessage as Mock).mockResolvedValue(
        mockResponse,
      );

      // Execute
      const result = await controller.postMessage(postMessageDto, botCtx);

      // Verify
      expect(result).toEqual([mockResponse]);
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        botCtx.installation.botToken,
        {
          blocks:
            '[{"type":"section","text":{"type":"mrkdwn","text":"Hello world"}}]',
          channel: 'C12345',
          text: 'Hello world',
          thread_ts: '1234567890.123456',
          unfurl_links: true,
          unfurl_media: true,
        },
      );
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (mockSlackWebAPIService.sendMessage as Mock).mockImplementation(() => {
        throw error;
      });

      // Execute and verify
      await expect(
        controller.postMessage(postMessageDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (mockSlackWebAPIService.sendMessage as Mock).mockRejectedValue(error);

      // Execute and verify
      await expect(
        controller.postMessage(postMessageDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('updateMessage', () => {
    const updateMessageDto: UpdateMessageDTO = {
      channel: 'C12345',
      ts: '1234567890.123456',
      text: 'Updated message',
      blocks: JSON.stringify([
        { type: 'section', text: { type: 'mrkdwn', text: 'Updated message' } },
      ]),
    };

    it('should update a message successfully', async () => {
      // Setup
      const mockResponse = { ts: '1234567890.123456', ok: true };
      (mockSlackWebAPIService.updateMessage as Mock).mockResolvedValue(
        mockResponse,
      );

      // Execute
      const result = await controller.updateMessage(updateMessageDto, botCtx);

      // Verify
      expect(result).toEqual(mockResponse);
      expect(mockSlackWebAPIService.updateMessage).toHaveBeenCalledWith(
        botCtx.installation.botToken,
        {
          channel: updateMessageDto.channel,
          text: updateMessageDto.text,
          ts: updateMessageDto.ts,
          blocks: JSON.parse(updateMessageDto.blocks as string),
        },
      );
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (mockSlackWebAPIService.updateMessage as Mock).mockImplementation(() => {
        throw error;
      });

      // Execute and verify
      await expect(
        controller.updateMessage(updateMessageDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (mockSlackWebAPIService.updateMessage as Mock).mockRejectedValue(error);

      // Execute and verify
      await expect(
        controller.updateMessage(updateMessageDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('deleteMessage', () => {
    const deleteMessageDto: DeleteMessageDTO = {
      channel: 'C12345',
      ts: '1234567890.123456',
    };

    it('should delete a message successfully', async () => {
      // Setup
      const mockResponse = { ok: true };
      (mockSlackWebAPIService.deleteMessage as Mock).mockResolvedValue(
        mockResponse,
      );

      // Execute
      const result = await controller.deleteMessage(deleteMessageDto, botCtx);

      // Verify
      expect(result).toEqual(mockResponse);
      expect(mockSlackWebAPIService.deleteMessage).toHaveBeenCalledWith(
        botCtx.installation.botToken,
        {
          channel: deleteMessageDto.channel,
          ts: deleteMessageDto.ts,
        },
      );
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (mockSlackWebAPIService.deleteMessage as Mock).mockImplementation(() => {
        throw error;
      });

      // Execute and verify
      await expect(
        controller.deleteMessage(deleteMessageDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (mockSlackWebAPIService.deleteMessage as Mock).mockRejectedValue(error);

      // Execute and verify
      await expect(
        controller.deleteMessage(deleteMessageDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('addReaction', () => {
    const addReactionDto: AddReactionDTO = {
      channel: 'C12345',
      ts: '1234567890.123456',
      name: 'thumbsup',
    };

    it('should add a reaction successfully', async () => {
      // Setup
      const mockResponse = { ok: true };
      (mockSlackWebAPIService.addReactionToMessage as Mock).mockResolvedValue(
        mockResponse,
      );

      // Execute
      const result = await controller.addReaction(addReactionDto, botCtx);

      // Verify
      expect(result).toEqual(mockResponse);
      expect(mockSlackWebAPIService.addReactionToMessage).toHaveBeenCalledWith(
        botCtx.installation.botToken,
        {
          channel: addReactionDto.channel,
          timestamp: addReactionDto.ts,
          name: addReactionDto.name,
        },
      );
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (mockSlackWebAPIService.addReactionToMessage as Mock).mockImplementation(
        () => {
          throw error;
        },
      );

      // Execute and verify
      await expect(
        controller.addReaction(addReactionDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (mockSlackWebAPIService.addReactionToMessage as Mock).mockRejectedValue(
        error,
      );

      // Execute and verify
      await expect(
        controller.addReaction(addReactionDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('removeReaction', () => {
    const removeReactionDto: RemoveReactionDTO = {
      channel: 'C12345',
      ts: '1234567890.123456',
      name: 'thumbsup',
    };

    it('should remove a reaction successfully', async () => {
      // Setup
      const mockResponse = { ok: true };
      (
        mockSlackWebAPIService.removeReactionFromMessage as Mock
      ).mockResolvedValue(mockResponse);

      // Execute
      const result = await controller.removeReaction(removeReactionDto, botCtx);

      // Verify
      expect(result).toEqual(mockResponse);
      expect(
        mockSlackWebAPIService.removeReactionFromMessage,
      ).toHaveBeenCalledWith(botCtx.installation.botToken, {
        channel: removeReactionDto.channel,
        timestamp: removeReactionDto.ts,
        name: removeReactionDto.name,
      });
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (
        mockSlackWebAPIService.removeReactionFromMessage as Mock
      ).mockImplementation(() => {
        throw error;
      });

      // Execute and verify
      await expect(
        controller.removeReaction(removeReactionDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (
        mockSlackWebAPIService.removeReactionFromMessage as Mock
      ).mockRejectedValue(error);

      // Execute and verify
      await expect(
        controller.removeReaction(removeReactionDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('inviteUserToConversation', () => {
    const inviteUserDto: InviteUserToConversationDTO = {
      channel: 'C12345',
      user: 'U12345',
    };

    it('should invite users to a conversation successfully', async () => {
      // Setup
      const mockResponse = { ok: true };
      (
        mockSlackWebAPIService.inviteUserToConversation as Mock
      ).mockResolvedValue(mockResponse);

      // Execute
      const result = await controller.inviteUserToConversation(
        inviteUserDto,
        botCtx,
      );

      // Verify
      expect(result).toEqual(mockResponse);
      expect(
        mockSlackWebAPIService.inviteUserToConversation,
      ).toHaveBeenCalledWith(botCtx.installation.botToken, {
        channel: inviteUserDto.channel,
        users: inviteUserDto.user,
      });
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (
        mockSlackWebAPIService.inviteUserToConversation as Mock
      ).mockImplementation(() => {
        throw error;
      });

      // Execute and verify
      await expect(
        controller.inviteUserToConversation(inviteUserDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (
        mockSlackWebAPIService.inviteUserToConversation as Mock
      ).mockRejectedValue(error);

      // Execute and verify
      await expect(
        controller.inviteUserToConversation(inviteUserDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('kickUserFromConversation', () => {
    const kickUserDto: KickUserFromConversationDTO = {
      channel: 'C12345',
      user: 'U12345',
    };

    it('should kick a user from a conversation successfully', async () => {
      // Setup
      const mockResponse = { ok: true };
      (
        mockSlackWebAPIService.kickUserFromConversation as Mock
      ).mockResolvedValue(mockResponse);

      // Execute
      const result = await controller.kickUserFromConversation(
        kickUserDto,
        botCtx,
      );

      // Verify
      expect(result).toEqual(mockResponse);
      expect(
        mockSlackWebAPIService.kickUserFromConversation,
      ).toHaveBeenCalledWith(botCtx.installation.botToken, {
        channel: kickUserDto.channel,
        user: kickUserDto.user,
      });
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (
        mockSlackWebAPIService.kickUserFromConversation as Mock
      ).mockImplementation(() => {
        throw error;
      });

      // Execute and verify
      await expect(
        controller.kickUserFromConversation(kickUserDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (
        mockSlackWebAPIService.kickUserFromConversation as Mock
      ).mockRejectedValue(error);

      // Execute and verify
      await expect(
        controller.kickUserFromConversation(kickUserDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('joinConversation', () => {
    const joinConversationDto: JoinConversationDTO = {
      channel: 'C12345',
      platformTeam: 'team-uid-1',
    };

    it('should join a conversation successfully', async () => {
      // Setup
      const mockResponse = { ok: true };
      const mockTeam = {
        id: 'team-1',
        uid: 'team-uid-1',
        organization: { id: 'org-1' },
        installation: { id: 'installation-1' },
      };
      const mockChannel = {
        id: 'channel-1',
        channelId: 'C12345',
        installation: { id: 'installation-1' },
        organization: { id: 'org-1' },
      };

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(mockTeam);
      (mockChannelsRepository.upsert as Mock).mockResolvedValue(undefined);
      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockTeamChannelMapsRepository.exists as Mock).mockResolvedValue(false);
      (mockTeamChannelMapsRepository.upsert as Mock).mockResolvedValue(
        undefined,
      );
      (mockSlackWebAPIService.joinConversation as Mock).mockResolvedValue(
        mockResponse,
      );

      // Execute
      const result = await controller.joinConversation(
        joinConversationDto,
        botCtx,
      );

      // Verify
      expect(result).toEqual(mockResponse);
      expect(mockTeamsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: joinConversationDto.platformTeam,
        },
      });
      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledWith(
        botCtx.installation.botToken,
        {
          channel: mockChannel.channelId,
        },
      );
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (mockSlackWebAPIService.joinConversation as Mock).mockImplementation(
        () => {
          throw error;
        },
      );

      // Execute and verify
      await expect(
        controller.joinConversation(joinConversationDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (mockSlackWebAPIService.joinConversation as Mock).mockRejectedValue(
        error,
      );

      // Execute and verify
      await expect(
        controller.joinConversation(joinConversationDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('leaveConversation', () => {
    const leaveConversationDto: LeaveConversationDTO = {
      channel: 'C12345',
    };

    it('should leave a conversation successfully', async () => {
      // Setup
      const mockResponse = { ok: true };
      (mockSlackWebAPIService.leaveConversation as Mock).mockResolvedValue(
        mockResponse,
      );

      // Execute
      const result = await controller.leaveConversation(
        leaveConversationDto,
        botCtx,
      );

      // Verify
      expect(result).toEqual(mockResponse);
      expect(mockSlackWebAPIService.leaveConversation).toHaveBeenCalledWith(
        botCtx.installation.botToken,
        {
          channel: leaveConversationDto.channel,
        },
      );
    });

    it('should handle HttpException errors', async () => {
      // Setup
      const error = new BadRequestException('Error message');
      (mockSlackWebAPIService.leaveConversation as Mock).mockImplementation(
        () => {
          throw error;
        },
      );

      // Execute and verify
      await expect(
        controller.leaveConversation(leaveConversationDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle general errors and log them', async () => {
      // Setup
      const error = new Error('Service error');
      (mockSlackWebAPIService.leaveConversation as Mock).mockRejectedValue(
        error,
      );

      // Execute and verify
      await expect(
        controller.leaveConversation(leaveConversationDto, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
